import { useState, useEffect } from "react"
import { logger } from "@/utils/logger"
import { cacheManager, cacheUtils } from "@/utils/cache"

// Импорт демо данных
import { products } from "@/data/products"

import { Product } from "@/types"

export interface ProductsResponse {
  success: boolean
  products: Product[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
  filters: {
    search: string
    category_id: string
    min_price: number
    max_price: number
    sort: string
  }
}

export interface ProductsFilters {
  page?: number
  limit?: number
  search?: string
  category_id?: string
  min_price?: number
  max_price?: number
  sort?: string
}

// Функция для трансформации продукта из API формата
function transformApiProduct(product: any): Product {
  return {
    id: product.product_id?.toString() || '',
    name: product.name || '',
    slug: generateSlug(product.name || ''),
    price: parseFloat(product.price) || 0,
    images: product.image_url ? [product.image_url] : product.image ? ['http://localhost/flowers_belka/image/' + product.image] : [],
    description: product.description || '',
    category: product.category_name || product.category_id?.toString() || 'bukety_tsvetov',
    subcategory: undefined,
    tags: [],
    isHit: product.isHit ||
           product.tags?.includes('hit') ||
           product.description?.toLowerCase().includes('букет') ||
           Math.random() > 0.7, // Простая временная логика для isHit
    inStock: (product.status === 1 && (product.quantity === null || product.quantity > 0)),
    shortDescription: product.description?.substring(0, 100) + '...',
    publishedAt: product.date_modified || product.date_added || new Date().toISOString(),
  }
}

// Функция для генерации slug из названия
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9а-я]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .substring(0, 50)
}

// Функция для фильтрации демо данных
function filterDemoProducts(products: any[], filters: ProductsFilters): ProductsResponse {
  let filtered = [...products]

  // Фильтр по поиску
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase()
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(searchTerm) ||
      product.description.toLowerCase().includes(searchTerm) ||
      product.shortDescription?.toLowerCase().includes(searchTerm)
    )
  }

  // Фильтр по категории
  if (filters.category_id && filters.category_id !== 'all') {
    filtered = filtered.filter(product => product.category === filters.category_id)
  }

  // Фильтр по цене
  if (filters.min_price !== undefined) {
    filtered = filtered.filter(product => product.price >= filters.min_price!)
  }
  if (filters.max_price !== undefined) {
    filtered = filtered.filter(product => product.price <= filters.max_price!)
  }

  // Сортировка
  if (filters.sort) {
    switch (filters.sort) {
      case 'price_asc':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price_desc':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'name_asc':
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'name_desc':
        filtered.sort((a, b) => b.name.localeCompare(a.name))
        break
      case 'created_desc':
        filtered.sort((a, b) => new Date(b.publishedAt || '').getTime() - new Date(a.publishedAt || '').getTime())
        break
      default:
        break
    }
  }

  // Пагинация
  const limit = filters.limit || 20
  const page = filters.page || 1
  const startIndex = (page - 1) * limit
  const paginatedProducts = filtered.slice(startIndex, startIndex + limit)

  return {
    success: true,
    products: paginatedProducts,
    pagination: {
      page,
      limit,
      total: filtered.length,
      pages: Math.ceil(filtered.length / limit)
    },
    filters: {
      search: filters.search || '',
      category_id: filters.category_id || 'all',
      min_price: filters.min_price || 0,
      max_price: filters.max_price || Number.MAX_VALUE,
      sort: filters.sort || 'name_asc'
    }
  }
}

export function useProducts(filters: ProductsFilters = {}) {
  const [products, setProducts] = useState<Product[]>([])
  const [pagination, setPagination] = useState<{
    page: number
    limit: number
    total: number
    pages: number
  }>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Инициализируем продукты из статических данных
  useEffect(() => {
    if (products.length === 0) {
      setProducts(products)
    }
  }, [])

  // Создаем cache key на основе фильтров
  const cacheKey = cacheUtils.productsKey(
    JSON.stringify({
      ...filters,
      timestamp: Math.floor(Date.now() / (1000 * 60 * 5)), // TTL = 5 минут
    })
  )

  const fetchProducts = async (useCache = true, retryCount = 0) => {
    const maxRetries = 3

    try {
      setIsLoading(true)
      setError(null)

      console.log('🚀 fetchProducts called with useCache:', useCache, 'retry:', retryCount)

      // Always use static data for migrated products
      const demoProductsData = filterDemoProducts(products, filters)

      // Сохраняем демо данные в кэш с меньшим TTL
      await cacheManager.set(cacheKey, demoProductsData, {
        ttl: 60, // 1 минута для демо данных
        tags: ["products", "demo", `category:${filters.category_id || "all"}`],
      })

      // Обновляем состояние
      setProducts(demoProductsData.products)
      setPagination(demoProductsData.pagination)
      setError(null) // Очищаем ошибку, так как есть данные

      logger.info("Products loaded from static migrated data", {
        count: demoProductsData.products.length,
        filters: Object.keys(filters).length,
      })

      return demoProductsData
    } catch (demoError) {
      const errorMessage = demoError instanceof Error ? demoError.message : "Unknown demo error"

      // Повторяем попытку при сетевых ошибках
      if (retryCount < maxRetries && (
        errorMessage.includes('network') ||
        errorMessage.includes('fetch') ||
        errorMessage.includes('timeout')
      )) {
        logger.warn(`Products fetch failed, retrying (${retryCount + 1}/${maxRetries})`, {
          error: errorMessage,
          filters: Object.keys(filters).length,
        })

        // Экспоненциальная задержка
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000)
        await new Promise(resolve => setTimeout(resolve, delay))

        return fetchProducts(useCache, retryCount + 1)
      }

      setError(errorMessage)

      logger.error("Static data failed", demoError instanceof Error ? demoError : undefined, {
        demoError: errorMessage,
        retryCount,
      })

      throw demoError
    } finally {
      setIsLoading(false)
    }
  }

  // Загружаем продукты при изменении фильтров
  useEffect(() => {
    fetchProducts()

    // Создаем подписки на изменения (кэш инвалидации)
    const handleCacheInvalidation = (event: CustomEvent) => {
      if (
        event.detail &&
        event.detail.tags &&
        event.detail.tags.some((tag: string) => tag.includes("products"))
      ) {
        logger.debug("Products cache invalidated, reloading", { cacheKey })
        fetchProducts(false) // Принудительная перезагрузка
      }
    }

    // Слушаем события инвалидации кэша
    document.addEventListener("cache-invalidation", handleCacheInvalidation)

    return () => {
      document.removeEventListener("cache-invalidation", handleCacheInvalidation)
    }
  }, [JSON.stringify(filters)])

  // Функции для работы с продуктами
  const invalidateCache = async () => {
    await cacheManager.invalidate(cacheKey)
    logger.info("Products cache manually invalidated", { cacheKey })
  }

  const refresh = () => {
    fetchProducts(false)
  }

  return {
    products,
    pagination,
    isLoading,
    error,
    refetch: fetchProducts,
    refresh,
    invalidateCache,
  }
}

// Хук для отдельных продуктов
export function useProduct(productId?: string) {
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const cacheKey = productId ? cacheUtils.productKey(productId) : null

  useEffect(() => {
    if (!productId || !cacheKey) return

    const fetchProduct = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Проверяем кэш
        const cachedProduct = await cacheManager.get<Product>(cacheKey, {
          ttl: 600, // 10 минут для отдельных продуктов
          tags: ["products"],
        })

        if (cachedProduct) {
          setProduct(cachedProduct)
          logger.debug("Product loaded from cache", { productId, cacheKey })
          return
        }

        // Загружаем из API
        const response = await fetch(
          `http://localhost/api_products.php?action=product&id=${productId}`
        )
        const data = await response.json()

        if (!data.success || !data.products || !data.products.length) {
          throw new Error(data.error || "Product not found")
        }

        const productData = transformApiProduct(data.products[0])

        // Сохраняем в кэш
        await cacheManager.set(cacheKey, productData, {
          ttl: 600,
          tags: ["products", `product:${productId}`],
        })

        setProduct(productData)
        logger.info("Product loaded from API", { productId })
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error"
        setError(errorMessage)

        logger.error("Product fetch failed", err instanceof Error ? err : new Error(errorMessage), {
          productId,
          cacheKey,
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchProduct()
  }, [productId, cacheKey])

  return {
    product,
    isLoading,
    error,
    refetch: () => productId && cacheKey,
  }
}

// Хук для категорий (добавлен бонусно)
export function useCategories() {
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const cacheKey = cacheUtils.categoriesKey()

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Проверяем кэш
        const cachedCategories = await cacheManager.get<any[]>(cacheKey, {
          ttl: 3600, // 1 час для категорий
          tags: ["categories"],
        })

        if (cachedCategories) {
          setCategories(cachedCategories)
          return
        }

        // Загружаем из API
        const response = await fetch("http://localhost/api_products.php?action=categories")
        const data = await response.json()

        if (!data.success) {
          throw new Error(data.error || "Failed to load categories")
        }

        // Сохраняем в кэш
        await cacheManager.set(cacheKey, data.categories, {
          ttl: 3600,
          tags: ["categories"],
        })

        setCategories(data.categories)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error"
        setError(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }

    fetchCategories()
  }, [cacheKey])

  return {
    categories: categories || [],
    isLoading,
    error,
  }
}
